<?php
// Start session only if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require 'koneksi.php';

// Check if user is logged in - allow all logged in users to use functions
// Access control will be handled in individual functions or calling files
if (!isset($_SESSION['nama'])) {
    // Only die if this is a direct POST request (AJAX call)
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        http_response_code(403);
        die('Access denied: Not logged in');
    }
    // For function calls from other files, let them handle the access control
}

/**
 * Grant file access permission to a user
 */
function grantFileAccess($koneksi, $file_id, $user_id, $permission_type, $granted_by) {
    // Check if permission already exists
    $check_query = "SELECT id FROM file_access_permissions 
                    WHERE file_id = ? AND user_id = ? AND permission_type = ?";
    $check_stmt = mysqli_prepare($koneksi, $check_query);
    mysqli_stmt_bind_param($check_stmt, "iis", $file_id, $user_id, $permission_type);
    mysqli_stmt_execute($check_stmt);
    $check_result = mysqli_stmt_get_result($check_stmt);
    
    if (mysqli_num_rows($check_result) > 0) {
        return ['success' => false, 'message' => 'Permission already exists'];
    }
    
    // Insert new permission
    $insert_query = "INSERT INTO file_access_permissions (file_id, user_id, permission_type, granted_by) 
                     VALUES (?, ?, ?, ?)";
    $insert_stmt = mysqli_prepare($koneksi, $insert_query);
    mysqli_stmt_bind_param($insert_stmt, "iisi", $file_id, $user_id, $permission_type, $granted_by);
    
    if (mysqli_stmt_execute($insert_stmt)) {
        return ['success' => true, 'message' => 'Permission granted successfully'];
    } else {
        return ['success' => false, 'message' => 'Failed to grant permission'];
    }
}

/**
 * Revoke file access permission from a user
 */
function revokeFileAccess($koneksi, $file_id, $user_id, $permission_type) {
    $delete_query = "DELETE FROM file_access_permissions 
                     WHERE file_id = ? AND user_id = ? AND permission_type = ?";
    $delete_stmt = mysqli_prepare($koneksi, $delete_query);
    mysqli_stmt_bind_param($delete_stmt, "iis", $file_id, $user_id, $permission_type);
    
    if (mysqli_stmt_execute($delete_stmt)) {
        return ['success' => true, 'message' => 'Permission revoked successfully'];
    } else {
        return ['success' => false, 'message' => 'Failed to revoke permission'];
    }
}

/**
 * Get all permissions for a file
 */
function getFilePermissions($koneksi, $file_id) {
    $query = "SELECT fap.*, p.nama_petugas as user_name, pg.nama_petugas as granted_by_name
              FROM file_access_permissions fap
              JOIN petugas p ON fap.user_id = p.id_petugas
              LEFT JOIN petugas pg ON fap.granted_by = pg.id_petugas
              WHERE fap.file_id = ?
              ORDER BY fap.created_at DESC";
    
    $stmt = mysqli_prepare($koneksi, $query);
    mysqli_stmt_bind_param($stmt, "i", $file_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $permissions = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $permissions[] = $row;
    }
    
    return $permissions;
}

/**
 * Check if user has specific permission for a file
 */
function hasFilePermission($koneksi, $file_id, $user_id, $permission_type) {
    $query = "SELECT id FROM file_access_permissions 
              WHERE file_id = ? AND user_id = ? AND permission_type = ?";
    $stmt = mysqli_prepare($koneksi, $query);
    mysqli_stmt_bind_param($stmt, "iis", $file_id, $user_id, $permission_type);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    return mysqli_num_rows($result) > 0;
}

/**
 * Get all files accessible by a user
 */
function getUserAccessibleFiles($koneksi, $user_id, $user_level) {
    if ($user_level == 'admin' || $user_level == 'proyek') {
        // Admin and project team can access all files
        $query = "SELECT fg.*, tp.nama_kegiatan, p.nama_petugas as uploader_name
                  FROM file_gambar fg
                  LEFT JOIN tugas_proyek tp ON fg.tugas_id = tp.id
                  LEFT JOIN petugas p ON fg.uploaded_by = p.id_petugas
                  ORDER BY fg.created_at DESC";
        $stmt = mysqli_prepare($koneksi, $query);
    } else {
        // Client can access all files (pending, approved, rejected, revision) for review
        // This allows client to see all files and provide feedback
        $query = "SELECT DISTINCT fg.*, tp.nama_kegiatan, p.nama_petugas as uploader_name
                  FROM file_gambar fg
                  LEFT JOIN tugas_proyek tp ON fg.tugas_id = tp.id
                  LEFT JOIN petugas p ON fg.uploaded_by = p.id_petugas
                  LEFT JOIN file_access_permissions fap ON fg.id = fap.file_id AND fap.user_id = ?
                  WHERE 1=1
                  ORDER BY fg.created_at DESC";
        $stmt = mysqli_prepare($koneksi, $query);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
    }
    
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $files = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $files[] = $row;
    }
    
    return $files;
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Check access for AJAX requests
    if (!isset($_SESSION['nama']) || ($_SESSION['level'] != 'admin' && $_SESSION['level'] != 'proyek')) {
        http_response_code(403);
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Access denied: Insufficient permissions']);
        exit;
    }

    $action = $_POST['action'] ?? '';
    $response = ['success' => false, 'message' => 'Invalid action'];
    
    switch ($action) {
        case 'grant_permission':
            $file_id = intval($_POST['file_id'] ?? 0);
            $user_id = intval($_POST['user_id'] ?? 0);
            $permission_type = $_POST['permission_type'] ?? '';
            $granted_by = $_SESSION['id_petugas'] ?? 0;
            
            if ($file_id > 0 && $user_id > 0 && in_array($permission_type, ['view', 'download', 'approve'])) {
                $response = grantFileAccess($koneksi, $file_id, $user_id, $permission_type, $granted_by);
            } else {
                $response = ['success' => false, 'message' => 'Invalid parameters'];
            }
            break;
            
        case 'revoke_permission':
            $file_id = intval($_POST['file_id'] ?? 0);
            $user_id = intval($_POST['user_id'] ?? 0);
            $permission_type = $_POST['permission_type'] ?? '';
            
            if ($file_id > 0 && $user_id > 0 && in_array($permission_type, ['view', 'download', 'approve'])) {
                $response = revokeFileAccess($koneksi, $file_id, $user_id, $permission_type);
            } else {
                $response = ['success' => false, 'message' => 'Invalid parameters'];
            }
            break;
            
        case 'get_permissions':
            $file_id = intval($_POST['file_id'] ?? 0);
            if ($file_id > 0) {
                $permissions = getFilePermissions($koneksi, $file_id);
                $response = ['success' => true, 'data' => $permissions];
            } else {
                $response = ['success' => false, 'message' => 'Invalid file ID'];
            }
            break;
    }
    
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}
?>
