<?php
require 'koneksi.php';

echo "<h2>Database Structure Test</h2>";

// Test file_gambar table structure
echo "<h3>1. Testing file_gambar table structure:</h3>";
$query = "DESCRIBE file_gambar";
$result = mysqli_query($koneksi, $query);

if ($result) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Error: " . mysqli_error($koneksi);
}

// Test new tables
echo "<h3>2. Testing new tables:</h3>";

$tables = ['file_access_permissions', 'file_comments'];
foreach ($tables as $table) {
    echo "<h4>Table: $table</h4>";
    $query = "SHOW TABLES LIKE '$table'";
    $result = mysqli_query($koneksi, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo "✅ Table exists<br>";
        
        // Show structure
        $desc_query = "DESCRIBE $table";
        $desc_result = mysqli_query($koneksi, $desc_query);
        
        if ($desc_result) {
            echo "<table border='1' cellpadding='3'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
            while ($row = mysqli_fetch_assoc($desc_result)) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "❌ Table does not exist<br>";
    }
}

// Test data
echo "<h3>3. Testing sample data:</h3>";

echo "<h4>File data:</h4>";
$query = "SELECT fg.id, fg.deskripsi, fg.status, fg.tugas_id, tp.nama_kegiatan 
          FROM file_gambar fg 
          LEFT JOIN tugas_proyek tp ON fg.tugas_id = tp.id 
          LIMIT 5";
$result = mysqli_query($koneksi, $query);

if ($result) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Deskripsi</th><th>Status</th><th>Tugas ID</th><th>Nama Kegiatan</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['deskripsi']) . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . $row['tugas_id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['nama_kegiatan']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Error: " . mysqli_error($koneksi);
}

echo "<h4>User data:</h4>";
$query = "SELECT id_petugas, nama_petugas, username, level FROM petugas LIMIT 5";
$result = mysqli_query($koneksi, $query);

if ($result) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Nama</th><th>Username</th><th>Level</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id_petugas'] . "</td>";
        echo "<td>" . htmlspecialchars($row['nama_petugas']) . "</td>";
        echo "<td>" . htmlspecialchars($row['username']) . "</td>";
        echo "<td>" . $row['level'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Error: " . mysqli_error($koneksi);
}

// Test foreign keys
echo "<h3>4. Testing foreign key constraints:</h3>";
$query = "SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('file_gambar', 'file_access_permissions', 'file_comments', 'verifikasi')";

$result = mysqli_query($koneksi, $query);

if ($result) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Table</th><th>Column</th><th>Constraint</th><th>Referenced Table</th><th>Referenced Column</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['TABLE_NAME'] . "</td>";
        echo "<td>" . $row['COLUMN_NAME'] . "</td>";
        echo "<td>" . $row['CONSTRAINT_NAME'] . "</td>";
        echo "<td>" . $row['REFERENCED_TABLE_NAME'] . "</td>";
        echo "<td>" . $row['REFERENCED_COLUMN_NAME'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Error: " . mysqli_error($koneksi);
}

echo "<hr>";
echo "<p><a href='test_session.php'>Test Session</a></p>";
echo "<p><a href='index.php'>← Back to Login</a></p>";
?>
