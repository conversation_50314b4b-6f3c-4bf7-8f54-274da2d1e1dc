<?php
// Start session only if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "client") {
    http_response_code(403);
    die('Access denied');
}

require '../koneksi.php';
require '../integration_handler.php';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    $response = ['success' => false, 'message' => 'Invalid action'];
    
    switch ($action) {
        case 'approve_file':
            $file_id = intval($_POST['file_id'] ?? 0);
            $comment = mysqli_real_escape_string($koneksi, $_POST['comment'] ?? '');
            $user_id = $_SESSION['id_petugas'] ?? 0;
            
            if ($file_id > 0) {
                // Start transaction
                mysqli_begin_transaction($koneksi);
                
                try {
                    // Update file status
                    $update_query = "UPDATE file_gambar 
                                   SET status = 'approved', 
                                       approved_by = ?, 
                                       approved_at = NOW(),
                                       client_notes = ?
                                   WHERE id = ?";
                    $update_stmt = mysqli_prepare($koneksi, $update_query);
                    mysqli_stmt_bind_param($update_stmt, "isi", $user_id, $comment, $file_id);
                    
                    if (!mysqli_stmt_execute($update_stmt)) {
                        throw new Exception('Failed to update file status');
                    }
                    
                    // Add comment if provided
                    if (!empty($comment)) {
                        $comment_query = "INSERT INTO file_comments (file_id, user_id, comment, comment_type) 
                                        VALUES (?, ?, ?, 'approval')";
                        $comment_stmt = mysqli_prepare($koneksi, $comment_query);
                        mysqli_stmt_bind_param($comment_stmt, "iis", $file_id, $user_id, $comment);
                        
                        if (!mysqli_stmt_execute($comment_stmt)) {
                            throw new Exception('Failed to add comment');
                        }
                    }
                    
                    // Update related task progress if applicable
                    $task_query = "UPDATE tugas_proyek tp 
                                 JOIN file_gambar fg ON tp.id = fg.tugas_id 
                                 SET tp.status = 'selesai', tp.progress_percentage = 100 
                                 WHERE fg.id = ? AND tp.status != 'selesai'";
                    $task_stmt = mysqli_prepare($koneksi, $task_query);
                    mysqli_stmt_bind_param($task_stmt, "i", $file_id);
                    mysqli_stmt_execute($task_stmt);
                    
                    mysqli_commit($koneksi);

                    // Update task progress and verification status
                    updateTaskProgressFromFileStatus($koneksi, $file_id);
                    updateVerificationFromFileStatus($koneksi, $file_id);

                    $response = ['success' => true, 'message' => 'File berhasil disetujui'];
                    
                } catch (Exception $e) {
                    mysqli_rollback($koneksi);
                    $response = ['success' => false, 'message' => 'Gagal menyetujui file: ' . $e->getMessage()];
                }
            } else {
                $response = ['success' => false, 'message' => 'Invalid file ID'];
            }
            break;
            
        case 'reject_file':
            $file_id = intval($_POST['file_id'] ?? 0);
            $comment = mysqli_real_escape_string($koneksi, $_POST['comment'] ?? '');
            $user_id = $_SESSION['id_petugas'] ?? 0;
            
            if ($file_id > 0 && !empty($comment)) {
                mysqli_begin_transaction($koneksi);
                
                try {
                    // Update file status
                    $update_query = "UPDATE file_gambar 
                                   SET status = 'rejected', 
                                       approved_by = ?, 
                                       approved_at = NOW(),
                                       client_notes = ?
                                   WHERE id = ?";
                    $update_stmt = mysqli_prepare($koneksi, $update_query);
                    mysqli_stmt_bind_param($update_stmt, "isi", $user_id, $comment, $file_id);
                    
                    if (!mysqli_stmt_execute($update_stmt)) {
                        throw new Exception('Failed to update file status');
                    }
                    
                    // Add comment
                    $comment_query = "INSERT INTO file_comments (file_id, user_id, comment, comment_type) 
                                    VALUES (?, ?, ?, 'rejection')";
                    $comment_stmt = mysqli_prepare($koneksi, $comment_query);
                    mysqli_stmt_bind_param($comment_stmt, "iis", $file_id, $user_id, $comment);
                    
                    if (!mysqli_stmt_execute($comment_stmt)) {
                        throw new Exception('Failed to add comment');
                    }
                    
                    // Update related task status
                    $task_query = "UPDATE tugas_proyek tp 
                                 JOIN file_gambar fg ON tp.id = fg.tugas_id 
                                 SET tp.status = 'proses' 
                                 WHERE fg.id = ?";
                    $task_stmt = mysqli_prepare($koneksi, $task_query);
                    mysqli_stmt_bind_param($task_stmt, "i", $file_id);
                    mysqli_stmt_execute($task_stmt);
                    
                    mysqli_commit($koneksi);

                    // Update task progress and verification status
                    updateTaskProgressFromFileStatus($koneksi, $file_id);
                    updateVerificationFromFileStatus($koneksi, $file_id);

                    $response = ['success' => true, 'message' => 'File berhasil ditolak'];
                    
                } catch (Exception $e) {
                    mysqli_rollback($koneksi);
                    $response = ['success' => false, 'message' => 'Gagal menolak file: ' . $e->getMessage()];
                }
            } else {
                $response = ['success' => false, 'message' => 'File ID dan komentar harus diisi'];
            }
            break;
            
        case 'request_revision':
            $file_id = intval($_POST['file_id'] ?? 0);
            $comment = mysqli_real_escape_string($koneksi, $_POST['comment'] ?? '');
            $user_id = $_SESSION['id_petugas'] ?? 0;
            
            if ($file_id > 0 && !empty($comment)) {
                mysqli_begin_transaction($koneksi);
                
                try {
                    // Update file status
                    $update_query = "UPDATE file_gambar 
                                   SET status = 'revision', 
                                       approved_by = ?, 
                                       approved_at = NOW(),
                                       client_notes = ?
                                   WHERE id = ?";
                    $update_stmt = mysqli_prepare($koneksi, $update_query);
                    mysqli_stmt_bind_param($update_stmt, "isi", $user_id, $comment, $file_id);
                    
                    if (!mysqli_stmt_execute($update_stmt)) {
                        throw new Exception('Failed to update file status');
                    }
                    
                    // Add comment
                    $comment_query = "INSERT INTO file_comments (file_id, user_id, comment, comment_type) 
                                    VALUES (?, ?, ?, 'revision')";
                    $comment_stmt = mysqli_prepare($koneksi, $comment_query);
                    mysqli_stmt_bind_param($comment_stmt, "iis", $file_id, $user_id, $comment);
                    
                    if (!mysqli_stmt_execute($comment_stmt)) {
                        throw new Exception('Failed to add comment');
                    }
                    
                    // Update related task status
                    $task_query = "UPDATE tugas_proyek tp 
                                 JOIN file_gambar fg ON tp.id = fg.tugas_id 
                                 SET tp.status = 'proses' 
                                 WHERE fg.id = ?";
                    $task_stmt = mysqli_prepare($koneksi, $task_query);
                    mysqli_stmt_bind_param($task_stmt, "i", $file_id);
                    mysqli_stmt_execute($task_stmt);
                    
                    mysqli_commit($koneksi);

                    // Update task progress and verification status
                    updateTaskProgressFromFileStatus($koneksi, $file_id);
                    updateVerificationFromFileStatus($koneksi, $file_id);

                    $response = ['success' => true, 'message' => 'Permintaan revisi berhasil dikirim'];
                    
                } catch (Exception $e) {
                    mysqli_rollback($koneksi);
                    $response = ['success' => false, 'message' => 'Gagal meminta revisi: ' . $e->getMessage()];
                }
            } else {
                $response = ['success' => false, 'message' => 'File ID dan komentar harus diisi'];
            }
            break;
            
        case 'get_file_details':
            $file_id = intval($_POST['file_id'] ?? 0);
            
            if ($file_id > 0) {
                // Get file details
                $file_query = "SELECT fg.*, tp.nama_kegiatan, p.nama_petugas as uploader_name
                             FROM file_gambar fg
                             LEFT JOIN tugas_proyek tp ON fg.tugas_id = tp.id
                             LEFT JOIN petugas p ON fg.uploaded_by = p.id_petugas
                             WHERE fg.id = ?";
                $file_stmt = mysqli_prepare($koneksi, $file_query);
                mysqli_stmt_bind_param($file_stmt, "i", $file_id);
                mysqli_stmt_execute($file_stmt);
                $file_result = mysqli_stmt_get_result($file_stmt);
                
                if ($file_result && mysqli_num_rows($file_result) > 0) {
                    $file_data = mysqli_fetch_assoc($file_result);
                    
                    // Get comments
                    $comment_query = "SELECT fc.*, p.nama_petugas as commenter_name
                                    FROM file_comments fc
                                    JOIN petugas p ON fc.user_id = p.id_petugas
                                    WHERE fc.file_id = ?
                                    ORDER BY fc.created_at DESC";
                    $comment_stmt = mysqli_prepare($koneksi, $comment_query);
                    mysqli_stmt_bind_param($comment_stmt, "i", $file_id);
                    mysqli_stmt_execute($comment_stmt);
                    $comment_result = mysqli_stmt_get_result($comment_stmt);
                    
                    $comments = [];
                    while ($comment = mysqli_fetch_assoc($comment_result)) {
                        $comments[] = $comment;
                    }
                    
                    $file_data['comments'] = $comments;
                    $response = ['success' => true, 'data' => $file_data];
                } else {
                    $response = ['success' => false, 'message' => 'File not found'];
                }
            } else {
                $response = ['success' => false, 'message' => 'Invalid file ID'];
            }
            break;
    }
    
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}
?>
