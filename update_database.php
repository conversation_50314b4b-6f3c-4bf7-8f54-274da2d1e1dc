<?php
require 'koneksi.php';

echo "<h2>Database Update Script</h2>";

// Check if new columns exist in file_gambar
echo "<h3>1. Checking file_gambar table structure...</h3>";

$columns_to_add = [
    'tugas_id' => 'int(11) DEFAULT NULL',
    'uploaded_by' => 'int(11) DEFAULT NULL',
    'file_size' => 'bigint DEFAULT NULL',
    'file_type' => 'varchar(50) DEFAULT NULL',
    'status' => "enum('pending','approved','rejected','revision') DEFAULT 'pending'",
    'client_notes' => 'text DEFAULT NULL',
    'approved_by' => 'int(11) DEFAULT NULL',
    'approved_at' => 'timestamp NULL DEFAULT NULL',
    'created_at' => 'timestamp NOT NULL DEFAULT current_timestamp()',
    'updated_at' => 'timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()'
];

foreach ($columns_to_add as $column => $definition) {
    $check_query = "SHOW COLUMNS FROM file_gambar LIKE '$column'";
    $result = mysqli_query($koneksi, $check_query);
    
    if (mysqli_num_rows($result) == 0) {
        echo "Adding column '$column'...<br>";
        $alter_query = "ALTER TABLE file_gambar ADD COLUMN $column $definition";
        if (mysqli_query($koneksi, $alter_query)) {
            echo "✅ Column '$column' added successfully<br>";
        } else {
            echo "❌ Error adding column '$column': " . mysqli_error($koneksi) . "<br>";
        }
    } else {
        echo "✅ Column '$column' already exists<br>";
    }
}

// Create file_access_permissions table
echo "<h3>2. Creating file_access_permissions table...</h3>";
$table_query = "CREATE TABLE IF NOT EXISTS `file_access_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `file_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `permission_type` enum('view','download','approve') NOT NULL DEFAULT 'view',
  `granted_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `file_id` (`file_id`),
  KEY `user_id` (`user_id`),
  KEY `granted_by` (`granted_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if (mysqli_query($koneksi, $table_query)) {
    echo "✅ file_access_permissions table created/verified<br>";
} else {
    echo "❌ Error creating file_access_permissions table: " . mysqli_error($koneksi) . "<br>";
}

// Create file_comments table
echo "<h3>3. Creating file_comments table...</h3>";
$table_query = "CREATE TABLE IF NOT EXISTS `file_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `file_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `comment` text NOT NULL,
  `comment_type` enum('general','revision','approval','rejection') DEFAULT 'general',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `file_id` (`file_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if (mysqli_query($koneksi, $table_query)) {
    echo "✅ file_comments table created/verified<br>";
} else {
    echo "❌ Error creating file_comments table: " . mysqli_error($koneksi) . "<br>";
}

// Add foreign key constraints
echo "<h3>4. Adding foreign key constraints...</h3>";

$constraints = [
    "ALTER TABLE `file_access_permissions` ADD CONSTRAINT `file_access_permissions_ibfk_1` FOREIGN KEY (`file_id`) REFERENCES `file_gambar` (`id`) ON DELETE CASCADE",
    "ALTER TABLE `file_access_permissions` ADD CONSTRAINT `file_access_permissions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `petugas` (`id_petugas`) ON DELETE CASCADE",
    "ALTER TABLE `file_access_permissions` ADD CONSTRAINT `file_access_permissions_ibfk_3` FOREIGN KEY (`granted_by`) REFERENCES `petugas` (`id_petugas`) ON DELETE SET NULL",
    "ALTER TABLE `file_comments` ADD CONSTRAINT `file_comments_ibfk_1` FOREIGN KEY (`file_id`) REFERENCES `file_gambar` (`id`) ON DELETE CASCADE",
    "ALTER TABLE `file_comments` ADD CONSTRAINT `file_comments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `petugas` (`id_petugas`) ON DELETE CASCADE",
    "ALTER TABLE `file_gambar` ADD CONSTRAINT `file_gambar_ibfk_1` FOREIGN KEY (`tugas_id`) REFERENCES `tugas_proyek` (`id`) ON DELETE SET NULL",
    "ALTER TABLE `file_gambar` ADD CONSTRAINT `file_gambar_ibfk_2` FOREIGN KEY (`uploaded_by`) REFERENCES `petugas` (`id_petugas`) ON DELETE SET NULL",
    "ALTER TABLE `file_gambar` ADD CONSTRAINT `file_gambar_ibfk_3` FOREIGN KEY (`approved_by`) REFERENCES `petugas` (`id_petugas`) ON DELETE SET NULL"
];

foreach ($constraints as $constraint) {
    // Extract constraint name for checking
    preg_match('/CONSTRAINT `([^`]+)`/', $constraint, $matches);
    $constraint_name = $matches[1] ?? '';
    
    if ($constraint_name) {
        // Check if constraint already exists
        $check_query = "SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                        WHERE CONSTRAINT_SCHEMA = DATABASE() AND CONSTRAINT_NAME = '$constraint_name'";
        $result = mysqli_query($koneksi, $check_query);
        
        if (mysqli_num_rows($result) == 0) {
            echo "Adding constraint '$constraint_name'...<br>";
            if (mysqli_query($koneksi, $constraint)) {
                echo "✅ Constraint '$constraint_name' added successfully<br>";
            } else {
                echo "❌ Error adding constraint '$constraint_name': " . mysqli_error($koneksi) . "<br>";
            }
        } else {
            echo "✅ Constraint '$constraint_name' already exists<br>";
        }
    }
}

// Update existing file data
echo "<h3>5. Updating existing file data...</h3>";
$update_query = "UPDATE file_gambar SET 
                 status = 'approved',
                 uploaded_by = 1,
                 file_type = LOWER(SUBSTRING_INDEX(gambar, '.', -1)),
                 created_at = NOW()
                 WHERE status IS NULL OR status = ''";

if (mysqli_query($koneksi, $update_query)) {
    $affected_rows = mysqli_affected_rows($koneksi);
    echo "✅ Updated $affected_rows existing file records<br>";
} else {
    echo "❌ Error updating file data: " . mysqli_error($koneksi) . "<br>";
}

echo "<h3>6. Database update completed!</h3>";
echo "<p>✅ All database updates have been applied successfully.</p>";

echo "<hr>";
echo "<p><a href='test_database.php'>Test Database Structure</a></p>";
echo "<p><a href='test_session.php'>Test Session</a></p>";
echo "<p><a href='index.php'>← Back to Login</a></p>";
?>
