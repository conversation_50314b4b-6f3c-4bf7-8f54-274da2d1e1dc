<?php
// Start session only if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h2>Session Debug Information</h2>";
echo "<h3>Session Status:</h3>";
echo "Session Status: " . session_status() . "<br>";
echo "Session ID: " . session_id() . "<br>";

echo "<h3>Session Variables:</h3>";
if (isset($_SESSION) && !empty($_SESSION)) {
    foreach ($_SESSION as $key => $value) {
        echo "$key: " . htmlspecialchars($value) . "<br>";
    }
} else {
    echo "No session variables found.<br>";
}

echo "<h3>Login Status Check:</h3>";
if (isset($_SESSION['nama'])) {
    echo "✅ User is logged in as: " . htmlspecialchars($_SESSION['nama']) . "<br>";
    echo "✅ User level: " . htmlspecialchars($_SESSION['level']) . "<br>";
    echo "✅ User ID: " . ($_SESSION['id_petugas'] ?? 'Not set') . "<br>";
} else {
    echo "❌ User is not logged in<br>";
}

echo "<h3>File Access Test:</h3>";
if (isset($_SESSION['nama'])) {
    require 'file_permissions.php';
    
    try {
        $user_id = $_SESSION['id_petugas'] ?? 0;
        $user_level = $_SESSION['level'];
        
        echo "Testing getUserAccessibleFiles function...<br>";
        $files = getUserAccessibleFiles($koneksi, $user_id, $user_level);
        echo "✅ Function executed successfully<br>";
        echo "Found " . count($files) . " accessible files<br>";
        
        if (count($files) > 0) {
            echo "<h4>Sample Files:</h4>";
            foreach (array_slice($files, 0, 3) as $file) {
                echo "- " . htmlspecialchars($file['deskripsi']) . " (Status: " . htmlspecialchars($file['status']) . ")<br>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error testing file access: " . $e->getMessage() . "<br>";
    }
} else {
    echo "Cannot test file access - user not logged in<br>";
}

echo "<h3>Database Connection Test:</h3>";
require 'koneksi.php';
if ($koneksi) {
    echo "✅ Database connection successful<br>";
    
    // Test query
    $test_query = "SELECT COUNT(*) as count FROM file_gambar";
    $result = mysqli_query($koneksi, $test_query);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "✅ Database query successful - Found " . $row['count'] . " files in database<br>";
    } else {
        echo "❌ Database query failed: " . mysqli_error($koneksi) . "<br>";
    }
} else {
    echo "❌ Database connection failed<br>";
}

echo "<h3>File System Test:</h3>";
$file_dir = "file_proyek/";
if (is_dir($file_dir)) {
    echo "✅ File directory exists: $file_dir<br>";
    $files = scandir($file_dir);
    $file_count = count($files) - 2; // Exclude . and ..
    echo "✅ Found $file_count files in directory<br>";
} else {
    echo "❌ File directory does not exist: $file_dir<br>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Login</a></p>";
echo "<p><a href='client/client.php'>Go to Client Dashboard</a></p>";
echo "<p><a href='proyek/proyek.php'>Go to Project Dashboard</a></p>";
echo "<p><a href='admin/admin.php'>Go to Admin Dashboard</a></p>";
?>
